from urls import router,sync_indexs
from fastapi import FastAPI
from fastapi.staticfiles import StaticFiles
from fastapi.templating import Jinja2Templates
from fastapi.middleware.cors import CORSMiddleware
from apscheduler.schedulers.background  import BackgroundScheduler

app = FastAPI(title="知识库管理系统", description="智能文档管理系统")

# 挂载静态文件目录
app.mount("/static", StaticFiles(directory="static"), name="static")

# 配置CORS中间件
app.add_middleware(CORSMiddleware,
    allow_origins=["*"],  # 允许所有域名访问
    allow_credentials=True,
    allow_methods=["*"],  # 允许所有 HTTP 方法
    allow_headers=["*"],  # 允许所有 HTTP 头
)

# 包含路由
app.include_router(router)

# 配置 Jinja2 模板引擎
templates = Jinja2Templates(directory="templates")

# 启动定时任务
# scheduler = BackgroundScheduler()
# scheduler.add_job(sync_indexs, 'interval', seconds=60, max_instances=1)
# scheduler.start()
# print("Scheduler started...")

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8009)
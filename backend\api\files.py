"""
文件相关API路由
"""
from fastapi import APIRouter, File, Form, UploadFile
from backend.services.file_service import file_service
from backend.services.ai_service import ai_service

router = APIRouter()


@router.post("/uploadfiles/")
async def upload_files(
    file: UploadFile = File(...), 
    type: str = Form(...), 
    data: str = Form(...)
):
    """文件上传"""
    return await file_service.upload_files(file, type, data)


@router.post("/getFileInfo/")
async def get_file_info(body: dict):
    """获取文件信息"""
    try:
        filename = body.get("filename", "")
        content = body.get("content", "")
        if not filename:
            return {"error": "文件名不能为空"}
        file_content = ''
        return await ai_service.get_file_info(filename, file_content)
    except Exception as e:
        print(f"获取文件信息失败: {str(e)}")
        return {"error": f"服务器内部错误: {str(e)}"}

@router.post("/savebs/")
async def save_bs(body: dict):
    """保存标书文件"""
    return await file_service.save_bs(body)
